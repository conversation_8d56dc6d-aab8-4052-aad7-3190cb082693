@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Contact Messages'])

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-lg-12 col-md-12">
                <div class="card mb-4">
                    <div class="d-flex justify-content-between px-3 py-4">
                        <h6 class="mb-0" style="color: #67748e;">Contact Messages</h6>
                    </div>

                    <div class="card-body p-3">
                        <x-data-table id="contactsTable" :ajax="route('contact.index')" :columns="[
                            [
                                'data' => 'DT_RowIndex',
                                'name' => 'DT_RowIndex',
                                'orderable' => false,
                                'searchable' => false,
                            ],
                            ['data' => 'full_name', 'name' => 'full_name'],
                            ['data' => 'email', 'name' => 'email'],
                            ['data' => 'phone', 'name' => 'phone'],
                            ['data' => 'subject', 'name' => 'subject'],
                            ['data' => 'status', 'name' => 'status'],
                            ['data' => 'created_at', 'name' => 'created_at'],
                            ['data' => 'actions', 'name' => 'actions', 'orderable' => false, 'searchable' => false],
                        ]" :order="[]">
                            <x-slot:header>
                                <th>S.No.</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </x-slot:header>
                        </x-data-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> --}}

    <script>
        $(document).ready(function() {
            const table = $('#contactsTable').DataTable();

            // Update counts after table loads
            table.on('draw', function() {
                updateStatusCounts();
            });

            function updateStatusCounts() {
                let pendingCount = 0;
                let viewedCount = 0;
                
                table.rows().every(function() {
                    const data = this.data();
                    if (data.status && data.status.includes('Pending')) {
                        pendingCount++;
                    } else if (data.status && data.status.includes('Viewed')) {
                        viewedCount++;
                    }
                });
                
                $('#pending-count').text(pendingCount);
                $('#viewed-count').text(viewedCount);
            }

            // Status toggle functionality
            $(document).on('click', '.toggle-status', function() {
                const id = $(this).data('id');
                const button = $(this);
                const currentStatus = button.text().trim();
                const newStatus = currentStatus === 'Viewed' ? 'Pending' : 'Viewed';

                Swal.fire({
                    title: `Change status to ${newStatus}?`,
                    text: "Are you sure you want to toggle the contact status?",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#aaa',
                    confirmButtonText: `Yes, make it ${newStatus}`,
                    cancelButtonText: 'Cancel',
                    scrollbarPadding: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        const originalHtml = button.html();
                        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Updating...');

                        $.ajax({
                            url: `{{ route('contact.status', ':id') }}`.replace(':id', id),
                            type: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.status) {
                                    // Reload the DataTable to reflect changes
                                    $('#contactsTable').DataTable().ajax.reload(null, false);

                                    Swal.fire({
                                        title: 'Updated!',
                                        text: `Status changed to ${response.newStatus}.`,
                                        icon: 'success',
                                        timer: 2000,
                                        showConfirmButton: false,
                                        scrollbarPadding: false
                                    });
                                } else {
                                    button.html(originalHtml);
                                    Swal.fire({
                                        title: 'Error!',
                                        text: response.error || 'Failed to update status.',
                                        icon: 'error',
                                        timer: 2000,
                                        showConfirmButton: false,
                                        scrollbarPadding: false
                                    });
                                }
                            },
                            error: function(xhr) {
                                button.html(originalHtml);
                                const errorMsg = xhr.responseJSON?.error || 'Failed to update status.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: errorMsg,
                                    icon: 'error',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            },
                            complete: function() {
                                button.prop('disabled', false);
                            }
                        });
                    }
                });
            });

            // Delete functionality
            $(document).on('click', '.delete-button', function() {
                const id = $(this).data('id');
                const row = $(this).closest('tr');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#aaa',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'Cancel',
                    scrollbarPadding: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: `{{ route('contact.destroy', ':id') }}`.replace(':id', id),
                            type: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.success) {
                                    table.row(row).remove().draw();
                                    updateStatusCounts();

                                    Swal.fire({
                                        title: 'Deleted!',
                                        text: response.success,
                                        icon: 'success',
                                        timer: 2000,
                                        showConfirmButton: false,
                                        scrollbarPadding: false
                                    });
                                }
                            },
                            error: function(xhr) {
                                const errorMsg = xhr.responseJSON?.error || 'Failed to delete contact message.';
                                Swal.fire({
                                    title: 'Error!',
                                    text: errorMsg,
                                    icon: 'error',
                                    timer: 2000,
                                    showConfirmButton: false,
                                    scrollbarPadding: false
                                });
                            }
                        });
                    }
                });
            });
        });
    </script>
@endsection
